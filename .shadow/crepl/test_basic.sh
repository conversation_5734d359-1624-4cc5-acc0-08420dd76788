#!/bin/bash

# 全面测试脚本
echo "编译crepl..."
cd "$(dirname "$0")"
make clean
make

if [ ! -f "./crepl-64" ]; then
    echo "编译失败！"
    exit 1
fi

echo "开始全面测试..."

# 测试1: 基本表达式
echo "=== 测试1: 基本表达式 ==="
cat << 'EOF' | ./crepl-64
hello
1
2
3
exit
EOF

echo ""

# 测试2: 函数定义和调用
echo "=== 测试2: 函数定义和调用 ==="
cat << 'EOF' | ./crepl-64
int f() { return 1; }
int g() { return 2; }
f()
g()
f() + g()
exit
EOF

echo ""

# 测试3: 递归函数
echo "=== 测试3: 递归函数 ==="
cat << 'EOF' | ./crepl-64
int fact(int n) { if (n == 0) return 1; else return fact(n - 1) * n; }
fact(3)
fact(4)
fact(5)
exit
EOF

echo ""

# 测试4: 复杂函数组合
echo "=== 测试4: 复杂函数组合 ==="
cat << 'EOF' | ./crepl-64
int gcd(int a, int b) { return b ? gcd(b, a % b) : a; }
gcd(12, 8)
gcd(fact(4), fact(3))
exit
EOF

echo ""
echo "测试完成！"
