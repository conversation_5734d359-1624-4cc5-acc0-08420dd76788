#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <dlfcn.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <signal.h>

// 最大函数定义数量
#define MAX_DEFINITIONS 100
// 临时文件名模板大小
#define TEMPLATE_SIZE 128

// 函数定义结构体
typedef struct {
    char* code;        // 函数源代码
    void* handle;      // 动态库句柄
} FunctionDef;

// 全局变量
FunctionDef functions[MAX_DEFINITIONS];  // 存储所有已定义的函数
int func_count = 0;                      // 当前函数数量
char* all_functions = NULL;              // 所有函数定义的字符串拼接

// 创建临时文件并返回文件描述符，同时生成对应的文件名
int create_temp_file(char* c_file, char* so_file) {
    // 创建临时文件模板（mkstemp要求以XXXXXX结尾）
    char temp_template[] = "/tmp/crepl-XXXXXX";

    int fd = mkstemp(temp_template);  // 创建临时文件
    if (fd < 0) {
        perror("mkstemp");
        return -1;
    }

    // 生成对应的.c和.so文件名
    snprintf(c_file, TEMPLATE_SIZE, "%s.c", temp_template);
    snprintf(so_file, TEMPLATE_SIZE, "%s.so", temp_template);

    // 关闭原始文件描述符
    close(fd);

    // 删除原始临时文件
    unlink(temp_template);

    // 创建.c文件
    int c_fd = open(c_file, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (c_fd < 0) {
        perror("open");
        return -1;
    }

    return c_fd;
}

// 检查输入字符串是否包含危险字符或过长
int validate_input(const char* input) {
    if (!input) return 0;

    size_t len = strlen(input);
    // 限制输入长度，防止缓冲区溢出
    if (len > 4000) {
        return 0;
    }

    // 检查是否包含危险的字符序列
    if (strstr(input, "#include") ||
        strstr(input, "system") ||
        strstr(input, "exec") ||
        strstr(input, "popen") ||
        strstr(input, "__asm__") ||
        strstr(input, "asm")) {
        return 0;
    }

    return 1;
}

// 安全的字符串拼接，防止缓冲区溢出
char* safe_string_concat(const char* str1, const char* str2, const char* str3) {
    if (!str1 && !str2 && !str3) return NULL;

    size_t len1 = str1 ? strlen(str1) : 0;
    size_t len2 = str2 ? strlen(str2) : 0;
    size_t len3 = str3 ? strlen(str3) : 0;

    // 添加额外空间用于换行符和结束符
    size_t total_len = len1 + len2 + len3 + 10;

    char* result = malloc(total_len);
    if (!result) {
        return NULL;
    }

    result[0] = '\0';  // 初始化为空字符串

    if (str1) strcat(result, str1);
    if (str2) {
        if (str1) strcat(result, "\n");
        strcat(result, str2);
    }
    if (str3) {
        if (str1 || str2) strcat(result, "\n");
        strcat(result, str3);
    }

    return result;
}

// 编译源文件为共享库
int compile_shared_lib(const char* c_file, const char* so_file) {
    pid_t pid = fork();  // 创建子进程
    if (pid < 0) {
        perror("fork");
        return -1;
    }

    if (pid == 0) {  // 子进程执行gcc编译
        // 重定向stderr到日志文件，避免编译错误信息干扰输出
        int err_fd = open("/tmp/crepl-err.log", O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (err_fd >= 0) {
            dup2(err_fd, STDERR_FILENO);
            close(err_fd);
        }

        // 执行gcc编译命令：gcc -fPIC -shared -o so_file c_file
        execlp("gcc", "gcc", "-fPIC", "-shared", "-o", so_file, c_file, NULL);
        perror("execlp");
        _exit(1);  // 如果exec失败，退出子进程
    }

    // 父进程等待编译完成
    int status;
    waitpid(pid, &status, 0);

    // 检查编译是否成功
    if (WIFEXITED(status) && WEXITSTATUS(status) == 0) {
        return 0;  // 编译成功
    }

    return -1;  // 编译失败
}

// 加载函数定义
void load_function(const char* func_code) {
    // 输入验证
    if (!validate_input(func_code)) {
        printf("Compile Error.\n");
        return;
    }

    // 检查函数数量限制
    if (func_count >= MAX_DEFINITIONS) {
        printf("Compile Error.\n");
        return;
    }

    // 创建临时文件名
    char c_temp[TEMPLATE_SIZE];
    char so_temp[TEMPLATE_SIZE];

    int c_fd = create_temp_file(c_temp, so_temp);
    if (c_fd < 0) {
        printf("Compile Error.\n");
        return;
    }

    // 构造完整的C源代码
    // 需要包含之前定义的所有函数，以支持函数间相互调用
    char* complete_code = safe_string_concat(all_functions, func_code, NULL);
    if (!complete_code) {
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }

    // 写入源文件
    if (write(c_fd, complete_code, strlen(complete_code)) < 0) {
        free(complete_code);
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    free(complete_code);
    close(c_fd);

    // 编译共享库
    if (compile_shared_lib(c_temp, so_temp) != 0) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 加载共享库
    void* handle = dlopen(so_temp, RTLD_LAZY | RTLD_GLOBAL);
    if (!handle) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 保存函数定义
    functions[func_count].code = strdup(func_code);  // 复制函数代码
    functions[func_count].handle = handle;           // 保存动态库句柄
    func_count++;

    // 更新所有函数定义字符串
    char* new_all_functions = safe_string_concat(all_functions, func_code, NULL);
    if (new_all_functions) {
        free(all_functions);
        all_functions = new_all_functions;
    } else {
        // 如果内存分配失败，回滚操作
        func_count--;
        free(functions[func_count].code);
        dlclose(handle);
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 成功加载后输出确认信息
    printf("Added: %s\n", func_code);

    // 清理临时文件
    unlink(c_temp);
    unlink(so_temp);
}

// 检查表达式是否为空或只包含空白字符
int is_empty_expression(const char* expr) {
    if (!expr) return 1;

    while (*expr) {
        if (*expr != ' ' && *expr != '\t' && *expr != '\n' && *expr != '\r') {
            return 0;
        }
        expr++;
    }
    return 1;
}

// 执行表达式求值
void eval_expression(const char* expr) {
    // 输入验证
    if (!validate_input(expr) || is_empty_expression(expr)) {
        printf("Compile Error.\n");
        return;
    }

    // 创建临时文件名
    char c_temp[TEMPLATE_SIZE];
    char so_temp[TEMPLATE_SIZE];

    int c_fd = create_temp_file(c_temp, so_temp);
    if (c_fd < 0) {
        printf("Compile Error.\n");
        return;
    }

    // 生成包装表达式的C代码
    // 需要包含所有已定义的函数，以支持表达式调用这些函数
    char header[] = "#include <stdio.h>\n";

    // 使用更安全的格式化方式
    size_t eval_func_len = strlen("int eval() { return ; }\n") + strlen(expr) + 1;
    char* eval_func = malloc(eval_func_len);
    if (!eval_func) {
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    snprintf(eval_func, eval_func_len, "int eval() { return %s; }\n", expr);

    // 构造完整的C代码
    char* c_code = safe_string_concat(header, all_functions, eval_func);
    free(eval_func);

    if (!c_code) {
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }

    // 写入源文件
    if (write(c_fd, c_code, strlen(c_code)) < 0) {
        free(c_code);
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    free(c_code);
    close(c_fd);

    // 编译共享库
    if (compile_shared_lib(c_temp, so_temp) != 0) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 加载共享库
    void* handle = dlopen(so_temp, RTLD_LAZY);
    if (!handle) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 获取eval函数指针
    int (*eval_func)() = dlsym(handle, "eval");
    if (!eval_func) {
        dlclose(handle);
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 执行表达式并输出结果
    int result = eval_func();
    printf("(%s) == %d.\n", expr, result);

    // 清理资源
    dlclose(handle);
    unlink(c_temp);
    unlink(so_temp);
}

// 判断输入是否为函数定义（以"int "开头）
int is_function_definition(const char* line) {
    if (!line) return 0;

    // 跳过前导空白字符
    while (*line == ' ' || *line == '\t') {
        line++;
    }

    return strncmp(line, "int ", 4) == 0;
}

// 去除字符串首尾的空白字符
void trim_whitespace(char* str) {
    if (!str) return;

    // 去除末尾空白字符
    size_t len = strlen(str);
    while (len > 0 && (str[len - 1] == ' ' || str[len - 1] == '\t' ||
                       str[len - 1] == '\n' || str[len - 1] == '\r')) {
        str[len - 1] = '\0';
        len--;
    }

    // 去除前导空白字符
    char* start = str;
    while (*start == ' ' || *start == '\t') {
        start++;
    }

    if (start != str) {
        memmove(str, start, strlen(start) + 1);
    }
}

// 检查是否为退出命令
int is_exit_command(const char* line) {
    if (!line) return 0;

    return (strcmp(line, "exit") == 0 ||
            strcmp(line, "quit") == 0 ||
            strcmp(line, "q") == 0);
}

// 安全的输入处理函数
char* process_input_line(char* line) {
    if (!line) return NULL;

    // 去除首尾空白字符
    trim_whitespace(line);

    // 检查是否为空行
    if (strlen(line) == 0) {
        return NULL;  // 表示跳过此行
    }

    return line;
}

// 清理所有资源
void cleanup_resources() {
    // 清理函数定义
    for (int i = 0; i < func_count; i++) {
        if (functions[i].code) {
            free(functions[i].code);
            functions[i].code = NULL;
        }
        if (functions[i].handle) {
            dlclose(functions[i].handle);
            functions[i].handle = NULL;
        }
    }
    func_count = 0;

    // 清理所有函数字符串
    if (all_functions) {
        free(all_functions);
        all_functions = NULL;
    }
}

// 信号处理函数，用于优雅退出
void signal_handler(int sig) {
    printf("\n");  // 换行
    cleanup_resources();
    exit(0);
}

int main(int argc, char *argv[]) {
    char *line = NULL;
    size_t len = 0;
    ssize_t read;

    // 设置信号处理，支持Ctrl+C优雅退出
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 主循环：读取输入并处理
    while (1) {
        printf("> ");  // 输出提示符（注意：根据测试用例要求是"> "而不是"crepl> "）
        fflush(stdout);

        // 读取一行输入
        read = getline(&line, &len, stdin);
        if (read == -1) {
            // EOF或读取错误，退出循环
            if (feof(stdin)) {
                break;  // 正常EOF
            } else {
                // 读取错误，但继续尝试
                clearerr(stdin);
                continue;
            }
        }

        // 处理输入行
        char* processed_line = process_input_line(line);
        if (!processed_line) {
            continue;  // 跳过空行
        }

        // 处理退出命令
        if (is_exit_command(processed_line)) {
            break;
        }

        // 根据输入类型进行处理
        if (is_function_definition(processed_line)) {
            // 函数定义：编译并加载到进程地址空间
            load_function(processed_line);
        } else {
            // 表达式：求值并输出结果
            eval_expression(processed_line);
        }
    }

    // 清理资源
    free(line);
    cleanup_resources();

    return 0;
}
