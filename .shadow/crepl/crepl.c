#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <dlfcn.h>
#include <sys/wait.h>
#include <sys/stat.h>
#include <assert.h>

#define MAX_DEFINITIONS 100
#define TEMPLATE_SIZE 128

typedef struct {
    char* code;
    void* handle;
} FunctionDef;

FunctionDef functions[MAX_DEFINITIONS];
int func_count = 0;
char* all_functions = NULL;

// 创建临时文件并返回文件描述符
int create_temp_file(char* template) {
    int fd = mkstemps(template, 0);
    if (fd < 0) {
        perror("mkstemps");
        return -1;
    }
    return fd;
}

// 编译源文件为共享库
int compile_shared_lib(const char* c_file, const char* so_file) {
    pid_t pid = fork();
    if (pid < 0) {
        perror("fork");
        return -1;
    }

    if (pid == 0) { // 子进程执行gcc
        int err_fd = open("/tmp/crepl-err.log", O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (err_fd < 0) {
            perror("open");
            _exit(1);
        }
        dup2(err_fd, STDERR_FILENO);
        close(err_fd);

        execlp("gcc", "gcc", "-fPIC", "-shared", "-o", so_file, c_file, NULL);
        perror("execlp");
        _exit(1);
    }

    // 父进程等待编译完成
    int status;
    waitpid(pid, &status, 0);
    if (WIFEXITED(status) && WEXITSTATUS(status) == 0) {
        return 0; // 编译成功
    }

    return -1; // 编译失败
}

// 加载函数定义
void load_function(const char* func_code) {
    // 创建临时文件
    char c_temp[TEMPLATE_SIZE] = "/tmp/crepl-func-XXXXXX.c";
    char so_temp[TEMPLATE_SIZE] = "/tmp/crepl-func-XXXXXX.so";

    int c_fd = create_temp_file(c_temp);
    if (c_fd < 0) {
        printf("Compile Error.\n");
        return;
    }

    // 写入函数代码
    if (write(c_fd, func_code, strlen(func_code)) < 0) {
        perror("write");
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    close(c_fd);

    // 创建共享库
    int so_fd = create_temp_file(so_temp);
    if (so_fd < 0) {
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    close(so_fd);

    // 编译共享库
    if (compile_shared_lib(c_temp, so_temp)) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 加载共享库
    void* handle = dlopen(so_temp, RTLD_LAZY | RTLD_GLOBAL);
    if (!handle) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 保存函数定义
    if (func_count < MAX_DEFINITIONS) {
        functions[func_count].code = strdup(func_code);
        functions[func_count].handle = handle;
        func_count++;

        // 更新所有函数定义字符串
        if (!all_functions) {
            all_functions = strdup(func_code);
        } else {
            char* new_all = malloc(strlen(all_functions) + strlen(func_code) + 2);
            sprintf(new_all, "%s\n%s", all_functions, func_code);
            free(all_functions);
            all_functions = new_all;
        }

        // 成功加载后输出
        printf("Added: %s\n", func_code);
    }

    // 清理临时文件
    unlink(c_temp);
    unlink(so_temp);
}

// 执行表达式求值
void eval_expression(const char* expr) {
    // 创建临时文件
    char c_temp[TEMPLATE_SIZE] = "/tmp/crepl-expr-XXXXXX.c";
    char so_temp[TEMPLATE_SIZE] = "/tmp/crepl-expr-XXXXXX.so";

    int c_fd = create_temp_file(c_temp);
    if (c_fd < 0) {
        printf("Compile Error.\n");
        return;
    }

    // 生成C代码
    char header[] = "#include <stdio.h>\n";
    char main_template[] = "int eval() { return %s; }\n";

    // 计算总长度
    size_t total_len = strlen(header) + (all_functions ? strlen(all_functions) : 0) +
                       strlen(main_template) + strlen(expr) + 1;

    char* c_code = malloc(total_len);
    if (!c_code) {
        perror("malloc");
        close(c_fd);
        printf("Compile Error.\n");
        return;
    }

    // 拼接代码
    strcpy(c_code, header);
    if (all_functions) {
        strcat(c_code, all_functions);
        strcat(c_code, "\n");
    }
    sprintf(c_code + strlen(c_code), main_template, expr);

    // 写入文件
    if (write(c_fd, c_code, strlen(c_code)) < 0) {
        perror("write");
        free(c_code);
        close(c_fd);
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    free(c_code);
    close(c_fd);

    // 创建共享库
    int so_fd = create_temp_file(so_temp);
    if (so_fd < 0) {
        unlink(c_temp);
        printf("Compile Error.\n");
        return;
    }
    close(so_fd);

    // 编译共享库
    if (compile_shared_lib(c_temp, so_temp)) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 加载共享库
    void* handle = dlopen(so_temp, RTLD_LAZY);
    if (!handle) {
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 获取eval函数
    int (*eval_func)() = dlsym(handle, "eval");
    if (!eval_func) {
        dlclose(handle);
        unlink(c_temp);
        unlink(so_temp);
        printf("Compile Error.\n");
        return;
    }

    // 执行并输出结果
    int result = eval_func();
    printf("(%s) == %d.\n", expr, result);

    // 清理
    dlclose(handle);
    unlink(c_temp);
    unlink(so_temp);
}

int main(int argc, char *argv[]) {
    char *line = NULL;
    size_t len = 0;
    ssize_t read;

    while (printf("> "), fflush(stdout), (read = getline(&line, &len, stdin)) != -1) {
        // 移除换行符
        if (read > 0 && line[read - 1] == '\n') {
            line[read - 1] = '\0';
        }

        // 检查空行
        if (strlen(line) == 0) {
            continue;
        }

        // 退出命令
        if (strcmp(line, "exit") == 0) {
            break;
        }

        // 函数定义
        if (strncmp(line, "int ", 4) == 0) {
            load_function(line);
        }
            // 表达式求值
        else {
            eval_expression(line);
        }
    }

    // 清理资源
    free(line);
    for (int i = 0; i < func_count; i++) {
        free(functions[i].code);
        dlclose(functions[i].handle);
    }
    free(all_functions);

    return 0;
}