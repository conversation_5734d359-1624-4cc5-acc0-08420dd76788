### 测试用例 1

**输入:**
```
hello
1
2
3
int f() { return 1; }
int g() { return 2; }
f()
g()
f() + g()
int fact(int n) { if (n == 0) return 1; else return fact(n - 1) * n; }
```

**期望输出:**
```
Compile Error.
(1) == 1.
(2) == 2.
(3) == 3.
Added: int f() { return 1; }
Added: int g() { return 2; }
(f()) == 1.
(g()) == 2.
(f() + g()) == 3.
Added: int fact(int n) { if (n == 0) return 1; else return fact(n - 1) * n; }
```

### 测试用例 2

**输入:**
```
fact(3)
fact(4)
fact(6)
fact(10)
fact(4) * fact(3)
int gcd(int a, int b) { return b ? gcd(b, a % b) : a; }
gcd(3, 4)
gcd(fact(3) * 128, fact(4))
```

**期望输出:**
```
(fact(3)) == 6.
(fact(4)) == 24.
(fact(6)) == 720.
(fact(10)) == 3628800.
(fact(4) * fact(3)) == 144.
Added: int gcd(int a, int b) { return b ? gcd(b, a % b) : a; }
(gcd(3, 4)) == 1.
(gcd(fact(3) * 128, fact(4))) == 24.
```

### 完整测试流程

1. **初始测试**:
   - 输入无效表达式 `hello` → 应输出 `Compile Error.`
   - 输入简单数字 `1`, `2`, `3` → 应正确输出值

2. **函数定义测试**:
   - 定义简单函数 `f()` 和 `g()`
   - 调用这些函数并验证结果
   - 测试函数组合 `f() + g()`

3. **递归函数测试**:
   - 定义阶乘函数 `fact()`
   - 测试不同参数的阶乘计算
   - 测试阶乘组合运算 `fact(4) * fact(3)`

4. **复杂函数测试**:
   - 定义GCD函数
   - 测试GCD计算
   - 测试GCD与阶乘的组合运算

### 注意事项

1. 阶乘函数定义在图片中有笔误，应为：
   ```c
   int fact(int n) { if (n == 0) return 1; else return fact(n - 1) * n; }
   ```
   (图片中误写为 `n == 8`)

2. 所有输出格式必须严格匹配：
   - 函数定义成功：`Added: <函数定义>`
   - 表达式求值：`(<表达式>) == <结果>.`
   - 编译错误：`Compile Error.`

3. 提示符应为简单的 `> `，不是 `crepl> `

4. 需要处理无效输入和边界情况

这些测试用例完整覆盖了您提供的图片中的所有交互场景，可以作为验证程序正确性的标准测试集。
