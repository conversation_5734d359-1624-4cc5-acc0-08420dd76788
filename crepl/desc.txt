实验描述
crepl - 逐行从 stdin 中输入单行 C 语言代码，并根据输入内容分别处理：

如果输入一个 C 函数的定义，则把函数编译并加载到进程的地址空间中；
如果输入是一个 C 语言表达式，则把它的值输出。

分如下两种情况：

(1) 函数
为了简化实验与操作系统无关的细节，如果输入的字符串 (一行) 以 int 开头，我们就认为它是一个函数。

int fib(int n) { return (n <= 1) ? 1 : fib(n - 1) + fib(n - 2); }

函数接收若干 int 类型的参数，返回一个 int 数值。如果一行是一个函数，我们希望它将会经过 gcc 编译，并被加载到当前进程的地址空间中。函数可以引用之前定义过的函数。

(2) 表达式
如果一行不是以 int 开头，我们就认为这一行是一个 C 语言的表达式 ，其类型为 int，例如

1 + 2 || (fib(3) * fib(4))

实验要求：函数和表达式均可以调用之前定义过的函数

正确性标准
你只要能正确解析单行的函数 (以 int 开头)，并且默认其他输入都是表达式即可。我们可能会输入不合法的 C 代码 (例如不合法的表达式)；你的程序应该给出错误提示而不应该 crash。你可以做出比较友好的假设——不像之前的实验，会为了 “强迫” 你掌握某些知识而使你疯狂 Wrong Answer。这个实验纯属放松，Online Judge 没有刁难你的测试用例，都和 demo 中的差不多。

注意我们允许函数和表达式调用之前 (在 crepl 中) 定义过的函数；
你可以假设我们输入的命令/表达式数量不超过 100 个；
注意你处在的运行目录可能没有写入的权限。建议你将创建的临时文件都放在 /tmp/ 目录下。建议使用 mkstemp family API 创建临时文件；
主进程确实求出了所有表达式的值。
禁止使用 C 标准库 system 和 popen。这稍稍增加了实验的难度，不过并没有增加多少。请把这个限制理解成强迫大家掌握操作系统知识的训练。
